import React, { useState, Ref, useEffect } from 'react';
import { Button, Tabs } from 'antd';
import { YTHForm, YTHPickUser } from 'yth-ui';
import type { Form } from '@formily/core/esm/models';
import { ActionType } from 'yth-ui/es/components/form/listFilter';
import { IYTHFormListColumnProps } from 'yth-ui/es/components/form/list';
import { CurrentUser } from '@/Constant';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import { Unit, User } from '@/service/system';
import baseApi from '@/service/baseApi';
import { IAction, IUserProps } from 'yth-ui/es/components/pickUser';
import style from './index.module.less';

const { TabPane } = Tabs;

// 定义学习对象类型
interface DicDataType {
  code: string;
  text: string;
}

const defaultQueryData: { learningObject: DicDataType[] } = {
  learningObject: [
    { code: 'A08A39A01', text: '主要负责人' },
    { code: 'A08A39A02', text: '安全管理人员' },
    { code: 'A08A39A03', text: '员工' },
    { code: 'A08A39A04', text: '承包商' },
  ],
};

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    learningObject?: DicDataType[];
  };
};

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC<PropsTypes> = ({
  onComplete, // 完成回调
  closeModal, // 关闭弹框方法
  defaultQuery = {},
}) => {
  // 当前激活的tab key
  const [activeTabKey, setActiveTabKey] = useState<string>('');
  const form: Form = YTHForm.createForm({});
  const currentRef: Ref<ActionType> = React.useRef();
  const PickRef: Ref<IAction> = React.useRef();

  // 获取learningObject数据，优先使用传入的defaultQuery，否则使用默认数据
  const learningObjectData: DicDataType[] =
    defaultQuery?.learningObject || defaultQueryData.learningObject;

  // 设置默认激活的tab（第一个选项）
  useEffect(() => {
    if (learningObjectData && learningObjectData.length > 0) {
      setActiveTabKey(learningObjectData[0].code);
    }
  }, [learningObjectData]);

  // tab切换处理函数
  const handleTabChange: (key: string) => void = (key: string) => {
    setActiveTabKey(key);
  };
  // 获取统一的列配置
  const getColumnsForTab: (tabItem: DicDataType) => IYTHFormListColumnProps[] = (
    tabItem: DicDataType,
  ) => {
    return [
      {
        title: '姓名',
        name: 'name',
        minWidth: 120,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
      {
        title: '对象类型',
        name: 'objectType',
        minWidth: 120,
        edit: false, // 不可编辑，自动填充
        componentName: 'Input' as const,
        componentProps: {
          disabled: true,
        },
        required: true,
        // 默认值设置为当前tab的名称
        defaultValue: tabItem.text,
      },
      {
        title: '工号',
        name: 'employeeId',
        minWidth: 120,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
      {
        title: '联系电话',
        name: 'phone',
        minWidth: 150,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
    ];
  };

  // 渲染tab内容
  const renderTabContent: (tabItem: DicDataType) => React.ReactNode = (tabItem) => {
    return (
      <YTHForm form={form}>
        <YTHForm.List
          columns={getColumnsForTab(tabItem)}
          name={`${tabItem.code}List`}
          rowOperations={[]}
          extra={[
            {
              key: 'addPersonnel',
              title: '安排',
              type: 'main',
              disabled: false,
              operation: async () => {
                PickRef?.current.click();
              },
            },
          ]}
          title=""
          actionRef={currentRef}
        />
      </YTHForm>
    );
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Tabs activeKey={activeTabKey} onChange={handleTabChange}>
        {learningObjectData.map((item: DicDataType) => (
          <TabPane tab={item.text} key={item.code}>
            {renderTabContent(item)}
          </TabPane>
        ))}
      </Tabs>
      <div className={style['pick-user-container']}>
        <YTHPickUser
          defaultOrganize={{
            id: CurrentUser()?.accountId,
            name: '',
            type: 'org',
          }}
          actionRef={PickRef}
          requestOrganize={async () => {
            const resData: Unit = await baseApi.getUnitTree();
            return formatTree(resData, 'unitType', 'unitName');
          }}
          searchMode="1"
          multiple
          onChange={(users) => {
            console.log(users);
          }}
          remoteSearch
          requestUser={async (organize) => {
            const resData: User[] = await baseApi.getUserList(organize.id);
            const newData: IUserProps[] | null = [];
            resData.forEach((item: User) => {
              newData.push({
                id: item.id,
                name: item.realName,
                type: 'user',
              });
            });
            return newData;
          }}
        />
      </div>

      <div>
        <Button
          onClick={() => {
            closeModal && closeModal();
          }}
        >
          关闭
        </Button>
        <Button>保存</Button>
      </div>
    </div>
  );
};

export default AssessmentPersonnelList;
